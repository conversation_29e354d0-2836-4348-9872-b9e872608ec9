name: Update Currencies

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  update-currencies:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.8'
          cache: 'pip'
          cache-dependency-path: .github/workflows/update-currencies/requirements.txt

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install requests

      - name: Run currency update script
        run: python .github/scripts/update_currencies.py

      - name: Check for file changes
        id: changes
        run: |
          if git diff --quiet; then
            echo "changed=false" >> $GITHUB_ENV
          else
            echo "changed=true"  >> $GITHUB_ENV
          fi

      - name: Create Pull Request
        if: env.changed == 'true'
        uses: peter-evans/create-pull-request@v7.0.8
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          branch: update-currencies
          base: main
          title: "Update currencies.json"
          commit-message: "chore: update currencies.json"
          path: backend/internal/core/currencies/currencies.json

      - name: No updates needed
        if: env.changed == 'false'
        run: echo "✅ currencies.json is already up-to-date"
