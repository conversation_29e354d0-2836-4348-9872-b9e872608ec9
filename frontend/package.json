{"private": true, "scripts": {"build": "nuxt generate", "dev": "nuxt dev", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint --ext \".ts,.js,.vue\" --ignore-path ../.gitignore --ignore-pattern \"components/ui\" .", "lint:fix": "eslint --ext \".ts,.js,.vue\" --ignore-path ../.gitignore --ignore-pattern \"components/ui\" . --fix", "lint:ci": "eslint --ext \".ts,.js,.vue\" --ignore-path ../.gitignore --ignore-pattern \"components/ui\" . --max-warnings 1", "typecheck": "pnpm vue-tsc --noEmit", "test:ci": "TEST_SHUTDOWN_API_SERVER=true vitest --run --config ./test/vitest.config.ts", "test:local": "TEST_SHUTDOWN_API_SERVER=false && vitest --run --config ./test/vitest.config.ts", "test:watch": " TEST_SHUTDOWN_API_SERVER=false vitest --config ./test/vitest.config.ts"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@iconify-json/mdi": "^1.2.3", "@intlify/unplugin-vue-i18n": "^4.0.0", "@nuxtjs/eslint-config-typescript": "^12.1.0", "@playwright/test": "^1.52.0", "@types/markdown-it": "^13.0.9", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vite-pwa/nuxt": "^0.5.0", "@vue/runtime-core": "^3.5.13", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-vue": "^9.33.0", "h3": "^1.7.1", "intl-messageformat": "^10.7.16", "isomorphic-fetch": "^3.0.0", "nuxt": "3.12.4", "prettier": "^3.5.3", "shadcn-nuxt": "0.11.3", "typescript": "5.6.2", "unplugin-icons": "^0.18.5", "vite-plugin-eslint": "^1.8.1", "vitest": "^1.6.1", "vue-i18n": "^9.14.4", "vue-tsc": "2.1.6"}, "dependencies": {"@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/tailwindcss": "^6.13.2", "@pinia/nuxt": "^0.5.5", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/lunr": "^2.3.7", "@vuepic/vue-datepicker": "^8.8.1", "@vueuse/core": "^12.8.2", "@vueuse/nuxt": "^10.11.1", "@vueuse/router": "^10.11.1", "@zxing/library": "^0.21.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dompurify": "^3.2.5", "fuzzysort": "^3.1.0", "h3": "^1.15.1", "http-proxy": "^1.18.1", "lucide-vue-next": "^0.474.0", "lunr": "^2.3.9", "markdown-it": "^14.1.0", "pinia": "^2.3.1", "postcss": "^8.5.3", "reka-ui": "^2.2.0", "semver": "^7.7.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "vaul-vue": "^0.4.1", "vue": "3.4.8", "vue-router": "^4.5.0", "vue-sonner": "^1.3.2"}}